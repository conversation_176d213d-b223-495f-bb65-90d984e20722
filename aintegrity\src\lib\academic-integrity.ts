/**
 * Academic Integrity Utilities
 * 
 * This module provides utilities for maintaining academic integrity
 * in AI tutoring interactions, including content analysis and response filtering.
 */

export interface AcademicIntegrityCheck {
  isViolation: boolean;
  violationType?: 'direct_answer' | 'homework_completion' | 'essay_writing' | 'test_cheating';
  confidence: number;
  explanation: string;
  suggestedResponse?: string;
}

/**
 * Common patterns that indicate academic integrity violations
 */
const VIOLATION_PATTERNS = {
  direct_answer: [
    /the answer is/i,
    /solution:\s*\d+/i,
    /equals?\s*\d+/i,
    /result:\s*\d+/i,
    /^x\s*=\s*\d+/i,
  ],
  homework_completion: [
    /here's your completed/i,
    /finished assignment/i,
    /solved all problems/i,
    /complete solution set/i,
  ],
  essay_writing: [
    /here's your essay/i,
    /complete paper/i,
    /finished writing/i,
    /your assignment:/i,
  ],
  test_cheating: [
    /test answer/i,
    /quiz solution/i,
    /exam response/i,
    /correct choice is/i,
  ]
};

/**
 * Educational source references for proper citation
 */
export const EDUCATIONAL_SOURCES = {
  mathematics: [
    'Khan Academy - https://www.khanacademy.org',
    '<PERSON>\'s Online Math Notes - https://tutorial.math.lamar.edu',
    'MIT OpenCourseWare - https://ocw.mit.edu',
    'Wolfram MathWorld - https://mathworld.wolfram.com',
    'Purplemath - https://www.purplemath.com'
  ],
  science: [
    'Khan Academy Science - https://www.khanacademy.org/science',
    'MIT OpenCourseWare - https://ocw.mit.edu',
    'NASA Educational Resources - https://www.nasa.gov/audience/foreducators',
    'National Science Foundation - https://www.nsf.gov',
    'Coursera Science Courses - https://www.coursera.org'
  ],
  writing: [
    'Purdue OWL - https://owl.purdue.edu',
    'Writing Center at UNC - https://writingcenter.unc.edu',
    'Harvard Writing Center - https://writingcenter.fas.harvard.edu',
    'MLA Style Center - https://style.mla.org',
    'APA Style - https://apastyle.apa.org'
  ],
  general: [
    'Library of Congress - https://www.loc.gov',
    'Smithsonian Learning - https://www.si.edu/educators',
    'TED-Ed - https://ed.ted.com',
    'Coursera - https://www.coursera.org',
    'edX - https://www.edx.org'
  ]
};

/**
 * Check if a response violates academic integrity principles
 */
export function checkAcademicIntegrity(response: string): AcademicIntegrityCheck {
  let highestConfidence = 0;
  let detectedViolation: keyof typeof VIOLATION_PATTERNS | null = null;

  // Check each violation type
  for (const [violationType, patterns] of Object.entries(VIOLATION_PATTERNS)) {
    for (const pattern of patterns) {
      if (pattern.test(response)) {
        const confidence = calculateConfidence(response, pattern);
        if (confidence > highestConfidence) {
          highestConfidence = confidence;
          detectedViolation = violationType as keyof typeof VIOLATION_PATTERNS;
        }
      }
    }
  }

  if (detectedViolation && highestConfidence > 0.7) {
    return {
      isViolation: true,
      violationType: detectedViolation,
      confidence: highestConfidence,
      explanation: getViolationExplanation(detectedViolation),
      suggestedResponse: getSuggestedTutoringResponse(detectedViolation)
    };
  }

  return {
    isViolation: false,
    confidence: 0,
    explanation: 'Response appears to follow academic integrity guidelines.'
  };
}

/**
 * Calculate confidence level for pattern match
 */
function calculateConfidence(text: string, pattern: RegExp): number {
  const matches = text.match(pattern);
  if (!matches) return 0;

  // Base confidence from pattern match
  let confidence = 0.8;

  // Increase confidence if multiple indicators present
  const indicators = [
    /\d+/g, // Numbers (suggesting direct answers)
    /step \d+/gi, // Step-by-step solutions
    /therefore/gi, // Conclusive language
    /final answer/gi, // Direct answer indicators
  ];

  for (const indicator of indicators) {
    if (indicator.test(text)) {
      confidence += 0.05;
    }
  }

  return Math.min(confidence, 1.0);
}

/**
 * Get explanation for violation type
 */
function getViolationExplanation(violationType: keyof typeof VIOLATION_PATTERNS): string {
  const explanations = {
    direct_answer: 'Response provides direct answers instead of guiding the learning process.',
    homework_completion: 'Response completes homework or assignments for the student.',
    essay_writing: 'Response writes essays or papers that students should complete themselves.',
    test_cheating: 'Response provides answers to test or quiz questions.'
  };

  return explanations[violationType];
}

/**
 * Get suggested tutoring response for violation type
 */
function getSuggestedTutoringResponse(violationType: keyof typeof VIOLATION_PATTERNS): string {
  const suggestions = {
    direct_answer: 'Instead of providing the answer, I should guide you through the problem-solving process. Let me help you understand the concepts and steps involved.',
    homework_completion: 'I can\'t complete your homework for you, but I can help you understand the concepts and provide guidance on how to approach the problems.',
    essay_writing: 'I can\'t write your essay, but I can help you brainstorm ideas, create an outline, and provide feedback on your draft.',
    test_cheating: 'I can\'t provide test answers, but I can help you study the relevant concepts and practice with similar problems.'
  };

  return suggestions[violationType];
}

/**
 * Generate appropriate source citations for a given topic
 */
export function generateSourceCitations(topic: string, sourceType: keyof typeof EDUCATIONAL_SOURCES = 'general'): string[] {
  const relevantSources = EDUCATIONAL_SOURCES[sourceType] || EDUCATIONAL_SOURCES.general;
  
  // Return 2-3 most relevant sources
  return relevantSources.slice(0, 3);
}

/**
 * Format a proper citation reminder
 */
export function formatCitationReminder(sources: string[]): string {
  if (sources.length === 0) return '';

  const citationText = sources.length === 1 
    ? `**Source:** ${sources[0]}`
    : `**Sources:**\n${sources.map(source => `- ${source}`).join('\n')}`;

  return `\n\n---\n${citationText}\n\n*Always verify information with your course materials and consult your instructor for specific requirements.*`;
}

/**
 * Validate that a tutoring response follows academic integrity guidelines
 */
export function validateTutoringResponse(response: string): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Check for direct answers
  if (/the answer is|solution:\s*\d+|equals?\s*\d+/i.test(response)) {
    issues.push('Contains direct answers');
    suggestions.push('Replace direct answers with guiding questions');
  }

  // Check for complete solutions
  if (/step 1.*step 2.*step 3/i.test(response) && /final answer/i.test(response)) {
    issues.push('Provides complete solution');
    suggestions.push('Show process with different examples instead');
  }

  // Check for source citations
  if (response.length > 200 && !/source|reference|according to/i.test(response)) {
    issues.push('Missing source citations');
    suggestions.push('Add appropriate source citations');
  }

  // Check for encouraging language
  if (!/can you|try|think about|what do you|let\'s/i.test(response)) {
    issues.push('Lacks interactive tutoring language');
    suggestions.push('Use more guiding questions and encouraging language');
  }

  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
}

/**
 * Generate a tutoring-appropriate response template
 */
export function generateTutoringTemplate(questionType: 'math' | 'writing' | 'science' | 'general'): string {
  const templates = {
    math: `I can see this is a [type] problem. Instead of giving you the answer, let me help you understand the approach:

1. First, let's identify what we know and what we're looking for...
2. What method do you think would work best here?
3. Here's a similar example with different numbers: [example]

Can you try applying these same steps to your problem?`,

    writing: `I can help you develop your ideas for this writing assignment! Instead of writing it for you, let's work together:

1. What's your main argument or thesis?
2. What evidence supports your position?
3. How might you organize these ideas?

I can help you create an outline and provide feedback on your draft once you've written it.`,

    science: `This is an interesting [concept] question! Let me guide you through the thinking process:

1. What scientific principles are involved here?
2. How do these principles apply to this situation?
3. Here's how we might approach a similar scenario: [example]

What do you think would happen if we applied this reasoning to your question?`,

    general: `Great question! Instead of just giving you information, let me help you explore this topic:

1. What do you already know about this subject?
2. What specific aspect interests you most?
3. Here are some reliable sources where you can learn more: [sources]

How would you like to approach learning about this?`
  };

  return templates[questionType];
}
